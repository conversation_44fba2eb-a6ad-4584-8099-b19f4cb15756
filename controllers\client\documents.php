<?php

	if($curr_client)
	{
		if($curr_client->id_company == 0){ $no_company = true; }

		$doc_type = strtolower($var2);
		$doc_action = $var3;
		$doc_date = $var4;

		$query = "SELECT * FROM client_shares WHERE id_client = $curr_client->id AND is_selected = 1";
		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			$share = $db->fetch_object($rs);
			$client_title = (strlen($curr_client->title)) ? $curr_client->title.' ' : '';

			//SPA

//			$share->recert_amount = (float)$share->recert_amount;
			$sdecs = substr($share->recert_amount, strpos($share->recert_amount, '.')+2);
			if($sdecs === '000')
				$share->recert_amount = number_format($share->recert_amount,2);
			else
				$share->recert_amount = (float)$share->recert_amount;


			$gross_sale_proceeds = $share->shares * $share->price;
			$recert_deposit_due = $share->shares * $share->recert_amount;
			$deductible_comission_fee = $gross_sale_proceeds * 0.015;
			$net_sale_proceeds = ($gross_sale_proceeds + $recert_deposit_due) - $deductible_comission_fee;


			//Margin Agreement - Open - refundable certificate
			$margin_client = $share->margin_open;
			$margin_company = 100 - $share->margin_open;

			$margin_client_total = $recert_deposit_due * ($margin_client / 100);
			$margin_company_total = $recert_deposit_due - $margin_client_total;
			$margin_net_sale = $net_sale_proceeds - $margin_company_total;

			if($margin_client > 0 && $doc_type!='spa')
				$net_sale_proceeds = $margin_net_sale;

			$has_margin_begin = ($margin_client > 0) ? '' : '<!--';
			$has_margin_end   = ($margin_client > 0) ? '' : '-->';



			//Warrants
			$wdecs = substr($share->warrant_price, strpos($share->warrant_price, '.')+2);
			if($wdecs === '000')
				$share->warrant_price = number_format($share->warrant_price,2);
			else
				$share->warrant_price = (float)$share->warrant_price;

			$warrants_due = $share->warrants * $share->warrant_price;
			$warrants_plus_shares = $share->shares + $share->warrants;

			$warrants_gross_sale = $warrants_plus_shares * $share->price;
			$warrants_comission = $warrants_gross_sale * 0.015;

			$warrants_net_sale = ($warrants_gross_sale - $warrants_comission) + $recert_deposit_due;

			if($margin_client)
				$warrants_net_sale -= $margin_company_total;



			//Margin Agreement - Warrants
			$margin_warrants_client = $share->margin_warrants;
			$margin_warrants_company = 100 - $share->margin_warrants;

			$margin_warrants_client_total = $warrants_due * ($margin_warrants_client / 100);
			$margin_warrants_company_total = $warrants_due - $margin_warrants_client_total;

			$margin_warrants_facility = $margin_warrants_company_total;

			if($margin_client)
				$margin_warrants_facility += $margin_company_total;

			$margin_warrants_net_sale = $warrants_net_sale - $margin_warrants_company_total;


			//:p
			$has_margin_warrants_begin = ($margin_warrants_client > 0) ? '' : '<!--';
			$has_margin_warrants_end   = ($margin_warrants_client > 0) ? '' : '-->';

			if($margin_warrants_client && $doc_type != 'warrants_co'){
				$warrants_net_sale = $margin_warrants_net_sale;
			}


			//Taxes QTAX
			$qtax_28 = (28/100) * $warrants_gross_sale;
			$qtax_total = $qtax_28 / 4;

			//Taxes SATAX
			$satax_total = $qtax_total * 2;


			//Margin Agreement - all taxes
			if(in_array($doc_type, ['qtax_margin','satax_margin','fulltax_margin',])===true){

				switch($doc_type){
					case 'qtax_margin': $tax_due = $qtax_total; break;
					case 'satax_margin': $tax_due = $satax_total; break;
					case 'fulltax_margin': $tax_due = $qtax_28; break;
					default: break;
				}

				$margin_tax_client = $share->margin_tax;
				$margin_tax_company = 100 - $share->margin_tax;

				$margin_tax_client_total = $tax_due * ($margin_tax_client / 100);
				$margin_tax_company_total = $tax_due - $margin_tax_client_total;

				$margin_tax_facility = $margin_tax_company_total;

				if($margin_client)
					$margin_tax_facility += $margin_company_total;

				if($margin_warrants_client)
					$margin_tax_facility += $margin_warrants_company_total;
/*
echo("<Br>MCT: $margin_company_total - $margin_client");
echo("<Br>MWT: $margin_warrants_company_total - $margin_warrants_client");
echo("<Br>MTCT: $margin_tax_company_total - $margin_tax_client");
echo("<Br>MTF: $margin_tax_facility");
//*/
				$margin_tax_net_sale = $warrants_net_sale - $margin_tax_company_total; //other margins are already discounted on warrantsnetsale

				$doc_type = 'margin_tax';
			}




			//Additional Shares
			$addshares_due = $share->addshares_amount * $share->addshares_price;
			$addshares_total = $share->addshares_amount * $share->addshares_clients;

			$addshares_plus_warrants = $warrants_plus_shares + $share->addshares_amount;
			$addshares_gross_sale = $addshares_plus_warrants * $share->price;
			$addshares_comission = $addshares_gross_sale * 0.015;

			$addshares_net_sale = ($addshares_gross_sale - $addshares_comission) + $recert_deposit_due;





			//check if client has reference
			$query_ref = "SELECT id_reference FROM clients WHERE id = $curr_client->id";
			$has_ref = $db->result($db->query($query_ref),0,0);
			if(!$has_ref)
				$no_ref = true;

			//check when/if SPA was sent
			$query_spa = "SELECT created_at,id FROM client_audit
								WHERE
									id_client = $curr_client->id AND
									event = 'update' AND
									field = 'id_status' AND
									new_value = '4'
								ORDER BY created_at DESC
								LIMIT 1";
			$spa_sent = @$db->result($db->query($query_spa),0,0);
			$spa_sent_id = @$db->result($db->query($query_spa),0,1);

			if(!strlen($spa_sent))
				$spa_not_sent = true;
			else{

				$date_spa_db = date('Y-m-d', strtotime($spa_sent));
				$date_spa_sent = date('F d, Y', strtotime($spa_sent));
			}

			if(!$share->ipg_date) $share->ipg_date = date('Y-m-d');


//			ALTER TABLE `client_shares`
//				ADD COLUMN `ipg_date` DATE NULL DEFAULT NULL AFTER `recert_amount`;


			$curr_client->company_url = str_replace("www.", "", $curr_client->company_url);
			$curr_client->registry_url = str_replace("www.", "", $curr_client->registry_url);


			//check if template exists
			$template_company_exists = is_dir("documents/templates/".parse_url($curr_client->company_url, PHP_URL_HOST));
			$template_registry_exists = is_dir("documents/templates/".parse_url($curr_client->registry_url, PHP_URL_HOST));

			if(strlen($doc_type) && strlen($doc_action))
			{

				//add shares <> liens
				$liens = false;
				$addshares_exhibit = 'EXHIBIT D';
				$addshares_liens = 'Additional Shares';
				if($doc_type == 'liens_reg' || $doc_type == 'liens_co'){
					$liens = true;
					$doc_type = str_replace('liens', 'addshares', $doc_type);
					$addshares_exhibit = 'EXHIBIT E';
					$addshares_liens = 'Liens';
				}

 				switch($doc_type)
				{
					case 'nda':
					{
						$signature1_img  = $curr_client->id_reference.'.png?'.time();
						// $signature1_img =  "sigref.png";
						$signature1_name = $curr_client->ref_name;
					//	$nda_custom_css = $curr_client->nda_custom_css;
						break;
					}

					//Registry Docs
					case 'ipg':
					case 'ipg-stamped':
					case 'bonded_guarantee':
					case 'default_notice':
					case 'warrants_reg2':
					case 'addshares_reg':
					{
						$doc_company_url = $curr_client->registry_url;

						$signature1_img =  'signature1.png?'.time();

						//ALTER TABLE `companies`	ADD COLUMN `director` VARCHAR(250) NULL AFTER `old_registry_id`;
						//update companies set director = 'Mr. Martin Cowan' where company_type = 'company'
						//update companies set director = 'Mr. Ronald Lewis' where company_type = 'registry'
						$signature1_name =  $curr_client->registry_director;
						if(!strlen($signature1_name)) $signature1_name = 'Ronald Lewis';


						$signature1_position =  'Director General';

						$signature2_img =  'signature2.png';
						$signature2_name = 'Mr. James Takumi'; // Default notary public name
						$signature2_position =  'Notary Public';

						$ipg_comm_exp_date = date('F d, Y', strtotime("+390 days"));

						if($doc_type=='ipg-stamped'){ $ipg_stamped = true; $doc_type = 'ipg'; }


						break;
					}

					case 'fulltax_reg':
					case 'satax_reg':
					case 'qtax_reg':
					case 'warrants_reg':
					{
						$doc_company_url = $curr_client->registry_url;

						$signature3_img =  'signature3.png?'.time();
						$signature3_name =  $curr_client->registry_compliance;
						if(!strlen($signature3_name)) $signature3_name = 'Mike Boich';
						$signature3_position =  'International Trade and Investment Facilitation Division';


						break;
					}


					//Company Docs
					case 'margin_warrants':
					case 'margin_open':
					case 'margin_tax':
					case 'fulltax_co':
					case 'satax_co':
					case 'qtax_co':
					case 'warrants_co':
					case 'spa':
					case 'pout':
					case 'srf':
					case 'addshares_co':
					{
						$doc_company_url = $curr_client->company_url;

						$signature1_img =  'signature1.png?'.time();

						//ALTER TABLE `companies`	ADD COLUMN `director` VARCHAR(250) NULL AFTER `old_registry_id`;
						//update companies set director = 'Mr. Martin Cowan' where company_type = 'company'
						//update companies set director = 'Mr. Ronald Lewis' where company_type = 'registry'
						$signature1_name =  $curr_client->company_director;
						if(!strlen($signature1_name)) $signature1_name = 'Mr. Martin Cowan';

						$signature1_position =  'Managing Director';


						$signature2_img =  'signature2.png';
						$signature2_name =  $curr_client->comp_manager;
						if(!strlen($signature2_name)) $signature2_name = 'Mr. George Lewis';

						$signature2_position =  'Sr. Manager, Compliance Department';
						break;
					}

				}



				$ts = time();

				//custom date for ipg
				if(
					$share->ipg_date &&
					($doc_type=='ipg' || $doc_type=='ipg-stamped')
				)
				{	$ts = strtotime($share->ipg_date); }

				//Date formats
				$date_regular = $date = date('d/m/Y',$ts);
//				$date_long = date('F dS, Y',$ts);
				$date_long = date('F d, Y',$ts);
				$date_long_p2 = date('F d, Y', strtotime("+2 days"));
				$date_stamp = date('Y  m  d',$ts);
				$date_Y = date('Y',$ts);
				$date_M = date('m',$ts);
				$date_D = date('d',$ts);

				$company_host = parse_url($doc_company_url, PHP_URL_HOST);
				$template_exists = is_dir("documents/templates/{$company_host}");

				if($_SERVER['SERVER_NAME'] == 'local')  $local_root = 'http://'.$_SERVER['HTTP_HOST'].$base;
				else									$local_root = $_SERVER['DOCUMENT_ROOT'].'/';

				$docs_url = $local_root."documents";
				$template_url = $local_root."documents/templates/{$company_host}";

				$doc_contents = file_get_contents("documents/content/{$doc_type}.php");
				$doc_template = file_get_contents("documents/basic_doc.php");

				if($doc_type != 'ref_sheet')
					$doc_css = file_get_contents("documents/basic.css");

				if($doc_type != 'nda' && $doc_type != 'ref_sheet' && $template_exists)
				{
					$doc_css .= @file_get_contents("{$template_url}/fonts.css");
					$doc_css .= @file_get_contents("{$template_url}/custom.css");
					$doc_header = file_get_contents("{$template_url}/header.php");
					$doc_footer = file_get_contents("{$template_url}/footer.php");
				}

				if($ipg_stamped)
					$stamp3 = "<img class=\"stamp3\" src=\"{$template_url}/imgs/stamp3.png\" />";


				$doc_search  = array(
										'|DOC_CONTENTS|','|DOC_CSS|','|HEADER|','|FOOTER|',
										'|CLIENT_NAME|','|CLIENT_ID|',

										'|DATE_REGULAR|', '|DATE_LONG|', '|DATE_STAMP|', '|DATE_LONG_PLUS_2|',
										'|DATE_Y|', '|DATE_M|', '|DATE_D|', '|DATE_SPA_SENT|',

										'|COMPANY_NAME|', '|COMPANY_ADDRESS|','|COMPANY_PHONE|','|COMPANY_AGENT_NAME|',
										'|COMPANY_FAX|','|COMPANY_EMAIL|','|COMPANY_URL|','|COMPANY_REG_NO|',
										'|COMPANY_CITY|','|COMPANY_NATIONALITY|','|COMPANY_COUNTRY|',

										'|REGISTRY_NAME|', '|REGISTRY_ADDRESS|','|REGISTRY_PHONE|',
										'|REGISTRY_FAX|','|REGISTRY_EMAIL|','|REGISTRY_URL|',
										'|REGISTRY_CITY|','|REGISTRY_NATIONALITY|','|REGISTRY_COUNTRY|',

										'|SHARE_NAME|', '|SHARE_QTY|', '|SHARE_PRICE|',
										'|SHARE_RECERT_AMOUNT|', '|SHARE_RECERT_DUE|',
										'|SHARE_TOTAL|','|COMISSION|','|NET_SALE|',

										'|DOCS_URL|','|TEMPLATE_URL|','|REFERENCE_NAME|',

										'|SIGNATURE1_IMG|','|SIGNATURE1_NAME|','|SIGNATURE1_POSITION|',
										'|SIGNATURE2_IMG|','|SIGNATURE2_NAME|','|SIGNATURE2_POSITION|',
										'|SIGNATURE3_IMG|','|SIGNATURE3_NAME|','|SIGNATURE3_POSITION|',

										'|STAMP-3|',
										'|IPG_COMM_EXP_DATE|',

										'|WARRANTS_QTY|','|WARRANT_PRICE|','|WARRANTS_DUE|',
										'|WARRANTS_GROSS_SALE|','|WARRANTS_PLUS_SHARES|','|WARRANTS_COMISSION|',
										'|WARRANTS_NET_SALE|',

										'|QTAX_28|','|QTAX_TOTAL|',

										'|SATAX_TOTAL|',

										'|MARGIN_CLIENT|','|MARGIN_CLIENT_TOTAL|',
										'|MARGIN_COMPANY|','|MARGIN_COMPANY_TOTAL|',
										'|MARGIN_NET_SALE|',

										'|MARGIN_WARRANTS_CLIENT|','|MARGIN_WARRANTS_CLIENT_TOTAL|',
										'|MARGIN_WARRANTS_COMPANY|','|MARGIN_WARRANTS_COMPANY_TOTAL|',
										'|MARGIN_WARRANTS_NET_SALE|',

										'|MARGIN_WARRANTS_FACILITY|',

										'|MARGIN_TAX_CLIENT|','|MARGIN_TAX_CLIENT_TOTAL|',
										'|MARGIN_TAX_COMPANY|','|MARGIN_TAX_COMPANY_TOTAL|',
										'|MARGIN_TAX_NET_SALE|',

										'|MARGIN_TAX_FACILITY|', '|TAX_DUE|',


										'|HAS_MARGIN_BEGIN|', '|HAS_MARGIN_END|',
										'|HAS_MARGIN_WARRANTS_BEGIN|', '|HAS_MARGIN_WARRANTS_END|',

										'|ADDSHARES_AMOUNT|','|ADDSHARES_PRICE|','|ADDSHARES_CLIENTS|','|ADDSHARES_TOTAL|',
										'|ADDSHARES_DUE|','|ADDSHARES_PLUS_WARRANTS|','|ADDSHARES_GROSS_SALE|',
										'|ADDSHARES_COMISSION|','|ADDSHARES_NET_SALE|',

										'|ADDSHARES_EXHIBIT|','|ADDSHARES_LIENS|'


									);

				$doc_replace = array(
										$doc_contents, $doc_css, $doc_header, $doc_footer,

										$client_title.$curr_client->name, $curr_client->id,
										$date_regular, $date_long, $date_stamp,$date_long_p2,
										$date_Y ,$date_M, $date_D, $date_spa_sent,

										$curr_client->company_name, $curr_client->company_address, $curr_client->company_phone,
										$curr_client->company_agent_name, $curr_client->company_fax, $curr_client->company_email,
										$curr_client->company_url,$curr_client->company_reg_no,
										$curr_client->company_city,$curr_client->company_nationality,$curr_client->company_country,

										$curr_client->registry_name, $curr_client->registry_address, $curr_client->registry_phone,
										$curr_client->registry_fax, $curr_client->registry_email, $curr_client->registry_url,
										$curr_client->registry_city,$curr_client->registry_nationality,$curr_client->registry_country,

										$share->name, number_format($share->shares),

										money($share->price), $share->recert_amount, money($recert_deposit_due),
										money($gross_sale_proceeds), money($deductible_comission_fee), money($net_sale_proceeds),

										$docs_url, $template_url, $curr_client->ref_name,

										$signature1_img, $signature1_name, $signature1_position,
										$signature2_img, $signature2_name, $signature2_position,
										$signature3_img, $signature3_name, $signature3_position,

										$stamp3,
										$ipg_comm_exp_date,

										number_format($share->warrants), $share->warrant_price, money($warrants_due),
										money($warrants_gross_sale), number_format($warrants_plus_shares), money($warrants_comission),
										money($warrants_net_sale),

										money($qtax_28), money($qtax_total),

										money($satax_total),

										$margin_client, money($margin_client_total),
										$margin_company, money($margin_company_total),
										money($margin_net_sale),

										$margin_warrants_client, money($margin_warrants_client_total),
										$margin_warrants_company, money($margin_warrants_company_total),
										money($margin_warrants_net_sale),

										money($margin_warrants_facility),

										$margin_tax_client, money($margin_tax_client_total),
										$margin_tax_company, money($margin_tax_company_total),
										money($margin_tax_net_sale),

										money($margin_tax_facility), money($tax_due),

										$has_margin_begin, $has_margin_end,
										$has_margin_warrants_begin, $has_margin_warrants_end,

										number_format($share->addshares_amount), money($share->addshares_price), number_format($share->addshares_clients), number_format($addshares_total),
										money($addshares_due), number_format($addshares_plus_warrants), money($addshares_gross_sale),
										money($addshares_comission), money($addshares_net_sale),

										$addshares_exhibit, $addshares_liens

									);

				$document = str_replace($doc_search, $doc_replace, $doc_template);

				switch($doc_action)
				{
					case 'preview':
					{
						if($_SERVER['SERVER_NAME'] != 'local')
							$document = str_replace($local_root, 'https://'.$_SERVER['HTTP_HOST'].$base, $document); //need that 4 images

						echo($document);
						exit();


						break;
					}

					case 'download':
					case 'save':
					{
						$sluggedname = strtolower(preg_replace('/[^A-Za-z0-9-]+/', '-', trim($curr_client->name)));
						$sluggedname_parts = explode('-', $sluggedname);

						$lastname = strtoupper($sluggedname_parts[count($sluggedname_parts)-1]);

						$use_regshortname = (in_array($doc_type, array('qtax', 'satax'))) ? true : false;
						if($use_regshortname)
						{
							$reg_shortname = preg_replace("/[^a-zA-Z]/", "", $curr_client->company_reg_no);
							$doc_type = $doc_type.'_'.$reg_shortname;
						}
						if($doc_type == 'warrants_co') $doc_type = 'Exhibit_C';
						if($doc_type == 'addshares_co') $doc_type = 'Exhibit_D';
						if($doc_type == 'addshares_co' && $liens == true) $doc_type = 'Exhibit_E';

						$doc_filename = strtoupper($doc_type)."_{$lastname}{$curr_client->id}_".date('Ymd');

						if($doc_type == 'ref_sheet'){
							$doc_filename .= '.doc';
							header("Content-type: application/vnd.ms-word");
							header("Content-Disposition: attachment;Filename={$doc_filename}");
							echo $document;
							exit;
						}
						else
							$doc_filename .= '.pdf';


						///*
						include('_lib/WkHtmlToPdf.php');
						$wk_options = array();

						$pdf = new WkHtmlToPdf($wk_options);
						$pdf->addPage($document);

						if($doc_action == 'download'){
							if(!$pdf->send($doc_filename))
								echo $pdf->getError();

						}

						if($doc_action == 'save'){

							$pdf->saveAs('./tmp_docs/'.$doc_filename);
							echo $pdf->getError();
							echo $doc_filename;
						}


						exit();
						break;
					}

				}//switch docaction

			} //strlen doctype&action

		} //end if share exists
		else $no_share = true;
	}



    /**
     * Create a tmp file with given content
     *
     * @param string $content the file content
     * @return string the path to the created file
     */
    function createTmpFile($content)
    {
        $tmpFile = tempnam('','tmp_WkHtmlToPdf_head');
        rename($tmpFile, ($tmpFile.='.html'));
        file_put_contents($tmpFile, $content);

        return $tmpFile;
    }
?>
