<?
	$action = $var1;
	$subaction = $var2;
	$id_share = $var3;

	if($curr_client->id):


		$sent_share = $_REQUEST['sent_share'];
		if($sent_share)
		{

			$fields_share = $_REQUEST['fields_share'];

			$fields_share['price'] = asNumber($fields_share['price']);
			$fields_share['total_value'] = asNumber($fields_share['total_value']);

			if($subaction == 'addshare')
			{
				$fields_share['created_at'] = date('Y-m-d H:i:s');
				$fields_share['id_client'] = $curr_client->id;

				$query = $db->prepare_insert($fields_share, 'client_shares');

				if($db->query($query))
					$msg = "Share successfully registered.";
			}

			if ( $subaction == 'editshare')
			{
				$fields_share['updated_at'] = date('Y-m-d H:i:s');

				$query = $db->prepare_update($fields_share, 'client_shares', "WHERE id = $id_share");

				if($db->query($query))
					$msg = "Share updated successfully.";
			}
		}

		if ( $subaction == 'deleteshare')
		{

			$query = "DELETE FROM client_shares WHERE id = $id_share";

			if($db->query($query))
				$msg = "Share deleted successfully.";
		}

		$query = "SELECT * FROM client_shares WHERE id_client = $curr_client->id ORDER BY is_selected DESC, created_at DESC";
		$rs_shares = $db->query($query);

		if($rs_shares && $db->num_rows($rs_shares) > 0)
			while ( $share = $db->fetch_object($rs_shares))
			{
				$shares[] = $share;
				if($subaction == 'editshare' && $id_share == $share->id) $curr_share = $share;
			}

	endif;


	function asNumber($val)
	{

		$replace1 = str_replace('$','',$val);
		return str_replace(',','',$replace1);

	}



?>