<?
	$id_user = $_REQUEST['id_user'];

	if($id_user)
	{
		$query = "SELECT DISTINCT(id_company) FROM company_employees WHERE is_active = '1' AND id_user = $id_user order by created_at DESC";
		$rs_user_companies = $db->query($query);
		if( $db->num_rows($rs_user_companies) )
		{
			while($user_company = $db->fetch_object($rs_user_companies))
				$user_companies .= $user_company->id_company.',';

			$user_companies = substr($user_companies,0,-1);


			$query = "SELECT id, name, company_type FROM companies WHERE is_active = '1' AND id IN($user_companies) ORDER BY company_type, name";
			$rs = $db->query($query);
			while( $r = $db->fetch_object($rs))
				$companies[$r->company_type][] = $r;



			if(isset($companies['company']) && is_array($companies['company']) && count($companies['company']) > 0):

				$str .= "<optgroup label='Companies'>";


				foreach($companies['company'] as $company):

					$str .= "<option value='$company->id'>- $company->name</option>\n";


				endforeach;

				$str .= "</optgroup>";
			endif;


			if(isset($companies['registry']) && is_array($companies['registry']) && count($companies['registry']) > 0):

				$str .= "<optgroup label='Registries'>";


				foreach($companies['registry'] as $company):

					$str .= "<option value='$company->id'>- $company->name</option>\n";


				endforeach;

				$str .= "</optgroup>";
			endif;


			$so_companies = $str;

		}

		$so_companies = (!strlen($so_companies)) ? '<option>No companies/registries found for this user.</option>' : '<option value=0>Select Company/Registry...</option>'.$so_companies;

		echo $so_companies;
	}
	else echo "<option value=''>You must select a company first.</option>";


?>
