<?php
	require_once 'form-config.php';

	// form submit
	if($_POST['submit']) {
		session_start();

		// $_POST to var
		$username = $_POST['username'];
		$password = $_POST['password'];

		// empty username
		if(empty($username)) {
			$error[] = $err_user;
		}

		// empty password
		if(empty($password)) {
			$error[] = $err_pass;
		}

		// username and password is not empty
		if(! empty($username) && !empty($password)) {
			// build data as array
			$arr1 = array($username => $password);

			// check if data key and value will match on $users
			if(array_intersect($users, $arr1)) {
				session_destroy();
				header('Location: ' . $redirect[$username]);
				die;
			} else {
				$error[] = $err_login;
			}
		}

		if( ! empty($error)) {

			$setupmsg = '';

			$cntr = isset($error) && is_array($error) ? count($error) : 0;

			for($a=0;$a<$cntr;$a++) {
				$setupmsg .= $error_msg;
			}

			$msg = vsprintf($error_parent['opening'] . $setupmsg . $error_parent['closing'], $error);

			echo $msg;
		}
	}
?>