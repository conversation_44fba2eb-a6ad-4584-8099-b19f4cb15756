<? if($no_client): ?>

		<div class="status warning">
        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
			<p>
				<img alt="Warning" src="<?=$base?>img/icons/icon_info.png">
				<span>No Client Selected.</span>
				Please select a client at the Client Search section.
			</p>
		</div>

<? else: ?>

        <!-- Content Box Start -->
        <div>
			<div class="contentcontainer">
                <h2>
					Client History for <?=$curr_client->name?>
					<? if($total_records):?><small>(<?=$total_records?> found)</small><? endif;?>
				</h2>
            </div>

			<div class="contentbox">

				<form id="recordSearch" method="post">

					<? //printr($filters); ?>

					<input type="hidden" name="filterSent" value="true" />
					<input type="text" class="inputbox smallbox" name="q"
						value="<?=$q?>"  placeholder="Search..." />

					<input type="submit" value="Apply Filters" class="btn" />

				</form>

				<br />

            	<table width="100%">
                	<thead>
                    	<tr>
                    		<? foreach($table_fields_parts as $table_field): ?>

                            	<th><?=$table_field?></th>

                    		<? endforeach; ?>

                    		<th></th>
                        </tr>
                    </thead>
                    <tbody>

                    	<? if(isset($records) && is_array($records) && count($records) > 0): ?>

						<?

							foreach($records as $r): if($r->id != 0):


								$current_field = $r->field;

						?>




							<tr <?=(($i++)%2)?"class='alt'":''?>>

        	            		<? foreach($table_fields_parts as $table_field): ?>

        	            		<?
        	            			switch($table_field)
        	            			{
        	            				case 'old_value':
        	            				case 'new_value':
        	            				{
        	            					if( $current_field=='id_employee' || $current_field=='id_employee_registry' )
        	            					{
        	            						if($r->{$table_field} == 0) $value = '--';
        	            						else
        	            						{
	        	            						$value = @$db->result(
	        	            									$db->query("SELECT email FROM company_employees WHERE id = ".$r->{$table_field})
	        	            								,0,0);
	        	            					}
        	            					}

        	            					if( $current_field=='id_status')
        	            					{
        	            						if($r->{$table_field} == 0) $value = '--';
        	            						else
        	            						{
	        	            						$query = "SELECT name FROM statuses WHERE id = ".$r->{$table_field};

	        	            						$value = @$db->result(
	        	            									$db->query($query)
	        	            								,0,'name');
        	            						}
        	            					}


        	            					if( $current_field=='id_reference')
        	            					{
        	            						if($r->{$table_field} == 0) $value = '--';
        	            						else
        	            						{
	        	            						$query = "SELECT name FROM refs WHERE id = ".$r->{$table_field};

	        	            						$value = @$db->result(
	        	            									$db->query($query)
	        	            								,0,'name');
        	            						}
        	            					}


        	            					if( $current_field == 'client note' || $current_field == 'notes')
        	            					{

	        	            						$value = str_replace('"', '', $r->{$table_field});

	        	            						$value = "<a class='tooltip' title=\"{$value}\">".substr($value, 0,50)."</a>";
	        	            						if(strlen($value) > 50) $value .= '...';
        	            					}

        	            					break;
        	            				}

        	            				default:
        	            				{

        	            					$value = $r->{$table_field};
	        	            				break;
        	            				}

        	            			}

        	            			if(!strlen($value))
	        	            			$value = $r->{$table_field};

        	            	//		$width = '';
        	            	//		if($table_field == 'id')	$width = "style='width:30px;'";
        	            	//		if($table_field == 'name' || $table_field == 'email' )	$width = "style='width:260px;'";

        	            		?>

    	                      		<td <?=$width?>><?=$value?></td>

	                    		<? endforeach; ?>
	                    		<td></td>
							</tr>

							<? endif; endforeach; ?>

						<? else: ?>

							<tr>
								<td colspan="8">

            						<div class="status info">
										<p>
											<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
											<span>No results found:</span>
											Make sure you have the right priveleges and all words are spelled correctly.
										</p>
									</div>

								</td>
							</tr>

						<? endif;?>


                    </tbody>
                </table>
 				<div class="spacer"></div>

                <ul class="pagination">

					<?=$pagination?>

                </ul>
                <div style="clear: both;"></div>
            </div>

        </div>
        <!-- Content Box End -->

<? endif; //noclient?>