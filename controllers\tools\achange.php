<?


	$leadsSent = $_REQUEST['leadsSent'];

	if($leadsSent)
	{
		$id_user = $_REQUEST['id_user'];
		$id_company = $_REQUEST['id_company'];
		$id_employee = $_REQUEST['id_employee'];

		if( intval($id_user) && intval($id_company))
		{

			$selectedLeads = explode(',',$_REQUEST['selectedLeads']);

			foreach($selectedLeads as $id_lead):

				if(intval($id_lead))
				{
					$valid_lead = $db->query("SELECT id, name, id_employee, id_employee_registry  FROM clients WHERE id = $id_lead");
					if($db->num_rows($valid_lead)==1)
					{
						$selected_lead_info = $db->fetch_object($valid_lead);

						$query = "SELECT company_type FROM companies WHERE id = $id_company";
						$company_type = $db->fetch_object($db->query($query));

						if($company_type->company_type == 'company')
						{
							$fields['id_company'] = $id_company;
							$fields['id_user_agent'] = $id_user;
							$fields['id_employee'] = $id_employee;
							$fields['id_registry'] = 0;
							$fields['id_user_registry'] = 0;
							$fields['id_employee_registry'] = 0;

							$audit_field =  'id_employee';
							$last_id = 	$selected_lead_info->id_employee;
						}

						if($company_type->company_type == 'registry')
						{
							$fields['id_registry'] = $id_company;
							$fields['id_user_registry'] = $id_user;
							$fields['id_employee_registry'] = $id_employee;
							$fields['id_user_agent'] = 0;
							$fields['id_employee'] = 0;

							$audit_field =  'id_employee_registry';
							$last_id = 	$selected_lead_info->id_employee_registry;
						}

						$query = $db->prepare_update($fields,'clients',"WHERE id = $id_lead LIMIT 1");
						if($db->query($query)) $msg .= "Lead {$id_lead}...ok - ";

						//update audit
						$fa['event'] = 'update';
						$fa['field'] = $audit_field;
						$fa['old_value'] = $last_id;
						$fa['new_value'] = $id_employee;
						$fa['id_client'] = $id_lead;
						$fa['id_user'] = $current_user['userinfo']->id;
						$fa['created_at'] = date('Y-m-d H:i:s');
						$query_audit = $db->prepare_insert($fa,'client_audit');
						@$db->query($query_audit);


					}
					else $msg .= "Lead $id_lead is invalid.<br>";

				}
				else $msg = 'No valid leads selected.';


			endforeach;



		}
		else $msg = 'Invalid user or company.';

	}


	///////////////////

	$updateStatus = $_REQUEST['updateStatus'];

	if($updateStatus)
	{
		$selectedLeads = explode(',',$_REQUEST['updateSelected']);

		$new_status = $_REQUEST['new-status'];

		if(isset($selectedLeads) && is_array($selectedLeads) && count($selectedLeads) > 0):

		$query = "SET SESSION sql_mode='NO_AUTO_VALUE_ON_ZERO'";
		$db->query($query);

		foreach($selectedLeads as $id_lead):

			if(intval($id_lead))
			{
				$valid_lead = $db->query("SELECT * FROM clients WHERE id = $id_lead");
				if($db->num_rows($valid_lead)==1)
				{
					$selected_lead_info = $db->fetch_object($valid_lead);

					$query = "UPDATE clients SET id_status = $new_status WHERE id = $id_lead";

				//	echo ("<br>$query");
					if($db->query($query)) $msg .= "Lead {$id_lead}...ok  ";


					//update audit
					$fa['event'] = 'update';
					$fa['field'] = 'id_status';
					$fa['old_value'] = $selected_lead_info->id_status;
					$fa['new_value'] = $new_status;
					$fa['id_client'] = $id_lead;
					$fa['id_user'] = $current_user['userinfo']->id;
					$fa['created_at'] = date('Y-m-d H:i:s');
					$query_audit = $db->prepare_insert($fa,'client_audit');
					@$db->query($query_audit);


					//Unassign users if new status = Wrong Number, TOL, Heat, SPA Cancelled, Dead Load
					$unassign_if_status = array(2,9,10,11,17);

					if(in_array($new_status,$unassign_if_status))
					{

						//Audit id employee
						$old_value = $selected_lead_info->id_employee;
						if($old_value != 0)
						{
							unset($fields_audit);
							$fields_audit['event'] = 'update';
							$fields_audit['field'] = 'id_employee';
							$fields_audit['new_value'] = 0;
							$fields_audit['old_value'] = $old_value;
							$fields_audit['id_client'] = $id_lead;
							$fields_audit['id_user'] = $current_user['userinfo']->id;
							$fields_audit['created_at'] = date('Y-m-d H:i:s');

							$query = $db->prepare_insert($fields_audit, 'client_audit');
							$db->query($query);
						}

						//Audit id employee Registry
						$old_value = $selected_lead_info->id_employee_registry;
						if($old_value != 0)
						{
							unset($fields_audit);
							$fields_audit['event'] = 'update';
							$fields_audit['field'] = 'id_employee_registry';
							$fields_audit['new_value'] = 0;
							$fields_audit['old_value'] = $old_value;
							$fields_audit['id_client'] = $id_lead;
							$fields_audit['id_user'] = $current_user['userinfo']->id;
							$fields_audit['created_at'] = date('Y-m-d H:i:s');

							$query = $db->prepare_insert($fields_audit, 'client_audit');
							$db->query($query);
						}

						//Update client
						$query = "UPDATE clients SET id_user_agent = 0, id_employee = 0, id_user_registry = 0, id_employee_registry = 0 WHERE id = $id_lead";
						$db->query($query);

					}


				}
				else $msg .= "Lead $id_lead is invalid.<br>";

			}
			else $msg = 'No valid leads selected.';


		endforeach;
		endif;

	}




	///////////////////

	$unassignLeads = $_REQUEST['unassignLeads'];

	if($unassignLeads)
	{
		$selectedLeads = explode(',',$_REQUEST['unassignSelected']);

		$query = "SET SESSION sql_mode='NO_AUTO_VALUE_ON_ZERO'";
		$db->query($query);

		if(isset($selectedLeads) && is_array($selectedLeads) && count($selectedLeads) > 0):
		foreach($selectedLeads as $id_lead):

			if(intval($id_lead))
			{
				$valid_lead = $db->query("SELECT * FROM clients WHERE id = $id_lead");
				if($db->num_rows($valid_lead)==1)
				{
					$selected_lead_info = $db->fetch_object($valid_lead);

					//Audit id employee
					$old_value = $selected_lead_info->id_employee;
					if($old_value != 0)
					{
						unset($fields_audit);
						$fields_audit['event'] = 'update';
						$fields_audit['field'] = 'id_employee';
						$fields_audit['new_value'] = 0;
						$fields_audit['old_value'] = $old_value;
						$fields_audit['id_client'] = $id_lead;
						$fields_audit['id_user'] = $current_user['userinfo']->id;
						$fields_audit['created_at'] = date('Y-m-d H:i:s');

						$query = $db->prepare_insert($fields_audit, 'client_audit');
						$db->query($query);
					}

					//Audit id employee Registry
					$old_value = $selected_lead_info->id_employee_registry;
					if($old_value != 0)
					{
						unset($fields_audit);
						$fields_audit['event'] = 'update';
						$fields_audit['field'] = 'id_employee_registry';
						$fields_audit['new_value'] = 0;
						$fields_audit['old_value'] = $old_value;
						$fields_audit['id_client'] = $id_lead;
						$fields_audit['id_user'] = $current_user['userinfo']->id;
						$fields_audit['created_at'] = date('Y-m-d H:i:s');

						$query = $db->prepare_insert($fields_audit, 'client_audit');
						$db->query($query);
					}

					//Update client


					$query = "UPDATE clients SET id_user_agent = 0, id_employee = 0, id_user_registry = 0, id_employee_registry = 0 WHERE id = $id_lead";
				//	echo ("<br>$query");
					if($db->query($query)) $msg .= "Lead {$id_lead}...ok  ";
				}
				else $msg .= "Lead $id_lead is invalid.<br>";

			}
			else $msg = 'No valid leads selected.';


		endforeach;
		endif;

	}






	$t1 = microtime();

	//Client Search
	$filterSent = $_REQUEST['filterSent'];
	$filters = $_SESSION['filters'] = ($filterSent) ? $_REQUEST['filters'] : $_SESSION['filters']; //if new filters were sent, get it from request and update session.

	//manage search filters
	if(isset($filters) && is_array($filters) && count($filters) > 0)
	{
		foreach($filters as $filter_name => $filter):

			if(!empty($filter) || $filter==='0'):

				switch($filter_name):

					case 'id':
					case 'id_status':
					case 'id_list':
					{

						$filter_clause .= "AND client.{$filter_name} = $filter ";
						break;
					}

					case 'id_user_agent':
					{

						if($filter == '0')
							$filter_clause .= "AND (client.id_user_agent = 0 AND client.id_user_registry = 0)";
						else
							$filter_clause .= "AND (client.id_user_agent = $filter OR client.id_user_registry = $filter)";


						break;
					}

					case 'client':
					case 'country':
					{
						$filter_clause .= "AND {$filter_name}.name LIKE '%$filter%' ";
						break;
					}


					case 'company':
					{

						$filter_clause .= "AND (company.name LIKE '%$filter%' OR registry.name LIKE '%$filter%')";
						break;
					}


					default: $filter_clause = NULL; break;

				endswitch;

			endif;

		endforeach;
	}

	//main_query
	$query = "SELECT
					client.id, client.title, client.name, client.id_status, client.id_list,

					company.name as company_name, company.address as company_address,

					company_agent.name as company_agent_name,
					company_agent.email as company_agent_email,

					registry.name as registry_name, registry.address as registry_address,

					registry_agent.name as registry_agent_name,
					registry_agent.email as registry_agent_email,


					company_user.username as company_user_name,
					registry_user.username as registry_user_name,

					country.iso3166 as country_name,
					status.name as status_name



				FROM
					clients client,
					countries country,
					companies company,
					companies registry,
					statuses status,
					users company_user,
					users registry_user,
					company_employees company_agent,
					company_employees registry_agent


				WHERE
						client.id_country = country.id
					AND client.id_company = company.id
					AND company.id_registry = registry.id
					AND client.id_status = status.id
					AND client.id_user_agent = company_user.id
					AND client.id_user_registry = registry_user.id
					AND client.id_employee = company_agent.id
					AND client.id_employee_registry = registry_agent.id


					$filter_clause

					ORDER by client.id
				";
					//AND client.is_active = 1
					//AND client.id_status IN(11,13)


	//manage pagination
	$rows_per_page = 50; //rows per page
	$page = ($var2)?$var2:1; //get current page from url (url/$interface/$var1/$var2 = url/client/search/page)

	$total_clients = $db->num_rows($db->query($query));
	$total_pages = ceil( $total_clients / $rows_per_page);

	for( $i=1; $i <= $total_pages; $i++)
	{
		$page_selected = ($i == $page) ? 'class="page"':'';
		$pagination .= "<li $page_selected><a href=\"{$base}tools/change/{$i}\">{$i}</a></li>";
	}

	if($page > 1) 			 $pagination  = "<li class=\"text\"><a href=\"{$base}tools/change/".($page-1).'">Previous</a></li>'.$pagination;
	if($page < $total_pages) $pagination .= "<li class=\"text\"><a href=\"{$base}tools/change/".($page+1).'">Next</a></li>';

	$limit_clause = 'LIMIT '.(($page-1) * $rows_per_page).','.$rows_per_page;
	$query .= $limit_clause;

	//filters and pagination added to query, now execute and add results to array
	$rs_clients = $db->query($query);
	while($client = $db->fetch_object($rs_clients))
		$clients[] = $client;

	$clients_on_page = (isset($clients) && is_array($clients) && count($clients)==$total_clients) ? ' All shown on this page.' : ' Showing '.(isset($clients) && is_array($clients) ? count($clients) : 0).' on this page.';


	//Load values for select fields
	$so['users'] = $db->select_values('id', 'username', 'users', $filters['id_user_agent'], 'WHERE is_active = 1 AND id_group <> 3'); //get active users except tech
	$so['statuses'] = $db->select_values('id', "CONCAT_WS(' - ', id, name)", 'statuses', $filters['id_status'], "WHERE is_active = 1 ORDER BY id"); //get active statuses
	$so['lists'] = $db->select_values('id', "CONCAT_WS(' - ', id, name)", 'lists', $filters['id_list'], 'WHERE is_active = 1 ORDER BY id DESC, name');	//get active loaders


	$t2 = microtime();
	$time = $t2-$t1;

?>