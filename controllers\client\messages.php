 <?php

	$query = "SELECT
					r.id, r.date, r.email_from, r.email_to, r.id_client, r.subject, r.message, r.attachs, r.is_processed, r.is_read, r.is_voicemail,
					c.name as client_name, c.id_user_agent, c.id_user_registry
				FROM
					client_replies r, clients c
				WHERE
					r.is_processed = 'yes' AND
					r.is_client = 'yes' AND
					r.id_client = c.id";

	$user_group = $current_user['userinfo']->id_group;
	if ( !in_array($user_group, array(1,3) )){
		$id_agent = $current_user['userinfo']->id;
		$query .= " AND (c.id_user_agent = $id_agent OR c.id_user_registry = $id_agent)";
	}

	if(intval($var2)){
		$view = 'client/message';
		$query .= " AND r.id = {$var2}";
	}

	$query .= " ORDER BY r.date DESC";
	$rs = $db->query($query);
	while($r = $db->fetch_object($rs)){
		$r->date = date('Y-m-d H:i', strtotime($r->date));
		$r->attachs = ($r->attachs) ? unserialize($r->attachs) : '';
		$emails[] = $r;
	}


	if(intval($var2)){
		if(isset($emails) && is_array($emails) && count($emails) > 0){
			$email = $emails[0];
			if($var3 == 'remove'){
				$db->query("DELETE FROM client_replies WHERE id = $email->id");
				header("location: {$base}client/messages");
				exit();
			}
			$db->query("UPDATE client_replies SET is_read = 'yes' WHERE id = {$email->id}");
		}
	}
