<?
	ini_set('max_execution_time', 0);
	//import leads from csv

/*

ALTER TABLE `lists`
	ADD COLUMN `status` VARCHAR(100) NULL AFTER `updated_at`,
	ADD COLUMN `total_new` VARCHAR(100) NULL DEFAULT '0' AFTER `status`,
	ADD COLUMN `total_duplicates` VARCHAR(100) NULL DEFAULT '0' AFTER `total_new`;


INSERT INTO `statuses` (`id`, `name`) VALUES (0, 'Unassigned');
CREATE TABLE `list_duplicates` (
	`id` INT(10) NOT NULL AUTO_INCREMENT,
	`list_name` VARCHAR(250) NOT NULL DEFAULT '0',
	`list_info` TEXT NULL,
	`possible_duplicates` TEXT NULL,
	PRIMARY KEY (`id`)
)
COLLATE='utf8_general_ci';


ALTER TABLE `lists`
	CHANGE COLUMN `updated_at` `updated_at` DATETIME NULL DEFAULT NULL AFTER `created_at`;
*/

	if( $_REQUEST['sent'] )
	{

		$list = $_REQUEST['list'];

		if(strlen($list['name']))
		{
			$list_exists = getId('lists', 'name', $list['name']);

			if(! $list_exists)
			{

				//create list
				$list['status'] = 'open';
				$list['created_at'] = date('Y-m-d H:i:s');
				$query = $db->prepare_insert($list, 'lists');

				if($db->query($query))
					$id_list = $db->insert_id();

			}
			else $msg = "A list with this name already exists. Please choose another one.";

//			$file = str_replace( '"', '', @file_get_contents( $_FILES['csv-file']['tmp_name'] ));
			$file = @file_get_contents( $_FILES['csv-file']['tmp_name'] );

			$lines = explode(PHP_EOL, $file);
			//$lines = preg_split( '/\r\n|\r|\n/', $file);


			//$labels = explode(',',array_shift($lines));
			$labels = [];
			if(isset($lines) && is_array($lines) && count($lines) > 0)
			{
				$l++;

				$db->query("SET SESSION sql_mode='NO_AUTO_VALUE_ON_ZERO'");

				foreach($lines as $line)
				{
					if(strlen($line) > 50)
					{
						$l++;

						//echo '<br>'.$line;
						//$fields = explode(',', $line);
						$fields = str_getcsv($line);

						if(count($fields) != count($labels)) continue;



						$info = array_combine($labels, $fields);



						$info['country'] = str_replace('The ', '', $info['country']);


						$calling_code = getCallingCode($info['country']);

						//phones
						unset($phones);
						$is_dup = false;
						$howmany = 4;
						for($i=1;$i<=$howmany;$i++)
						{
							$phone = preg_replace( '/[^0-9]/', '', $info['phone'.$i]);
							$phone = str_replace(' ', '', $phone);

							if(strlen($phone) > 5)
							{
								$is_dup = getPhone($phone); if($is_dup) break;

								if(substr($phone, 0, 2) == '00')
								{
									$phone = substr($phone,2);
									$is_dup = getPhone($phone); if($is_dup) break;
								}



								if( substr($phone, 0, strlen($calling_code) ) != $calling_code && strlen($calling_code) && strlen($phone) <= 10)
								{
									$phone = $calling_code.$phone;
									$is_dup = getPhone($phone); if($is_dup) break;
								}

								$phones[] = $phone;

							}

						}
						if($is_dup)
						{
							$query = "SELECT id, name FROM clients WHERE id = $is_dup";
							$rs = $db->query($query);
							$r = $db->fetch_object($rs);

							$p_dup++;

							unset($dup_info);
							$dup_info['list_line'] = $l;
							$dup_info['list_name'] = $info['name'];
							$dup_info['db_id'] = $r->id;
							$dup_info['db_name'] = $r->name;

							if($r->id){
								$query = "update clients set id_list = $id_list WHERE id = $r->id";
								$rs = $db->query($query);
							}

							$dup_phones[] = $dup_info;
							//'<br>Name on List:'.$info['name'].' - Duplicate on DB: '.$dup_info;

						}





						if(isset($phones) && is_array($phones) && count($phones) > 0 && !$is_dup)
						{

							$z++;

							$name = $db->real_escape_string(str_replace('  ', ' ', $info['name']));
							$possible_dups = getClient($name);

							if($possible_dups)
							{
								$dupli['id_list'] = $id_list;
								$dupli['list_name'] = $name;
								$dupli['list_info'] = implode(';',$info);

								foreach($possible_dups as $dup)
									$dup_record .= "$dup->id, $dup->name, $dup->status_name, $dup->list_name ||";


								$dupli['possible_duplicates'] = $dup_record;

								$query = $db->prepare_insert($dupli, 'list_duplicates');
								$db->query($query);

								$i_dup++;


								unset($dup_info);
								$dup_info['list_line'] = $l;
								$dup_info['list_name'] = $info['name'];
								$dup_info['db_name'] = $dup_record;

								$dup_names[] = $dup_info;


								unset($dup_record);
							}
							else
							{


								$client = configRecord($info);
								$client['id_list'] = $id_list;

								$query = $db->prepare_insert($client, 'clients');
								$db->query($query);

								$id_client = $db->insert_id();

								foreach($phones as $phone)
								{
									if(strlen($phone))
									{
										$db_contact = configContact($phone, $id_client, 'phone');
										$query = $db->prepare_insert($db_contact, 'client_contacts');
										$db->query($query);
									}
								}

								if(strlen($info['email1']))
								{
									$db_contact = configContact($info['email1'], $id_client, 'email');
									$query = $db->prepare_insert($db_contact, 'client_contacts');
									$db->query($query);
								}


								if(strlen($info['fax']))
								{
									$db_contact = configContact($info['fax'], $id_client, 'phone', 2);
									$query = $db->prepare_insert($db_contact, 'client_contacts');
									$db->query($query);
								}


								unset($shares);
								$shares = configShares($info, $id_client, 12);
								if(isset($shares) && is_array($shares) && count($shares) > 0)
								{
									foreach($shares as $share)
									{
										$query = $db->prepare_insert($share, 'client_shares');
										$db->query($query);
									}

								}
								//stocks

								$i_client++;

							}
						} //sizeof phones
						else
						{

								//printr($phones);
	 							//echo '<br>'.$line;
								//$fields = explode(',', $line);
								//printr($fields);
								//$info = array_combine($labels, $fields);
						}
					}
				}

				if($id_list) $db->query("UPDATE lists SET total_new = '$i_client', total_duplicates = '$i_dup' WHERE id = $id_list");

			}
		}
	}
	//die();


	// config stocks
	function configShares($info, $id_client, $howmany)
	{
		global $db;

		for($i=1;$i<=$howmany;$i++)
		{
			if( $info["t{$i}stock"] )
			{


				$name = $info["t{$i}stock"];
				$shares = intval(str_replace(',','',$info["t{$i}share"]));
				$price = (strlen($info["t{$i}pps"])) ? str_replace(',','.',$info["t{$i}pps"]) : 0;
				$price = floatval(str_replace('$', '', $price));

				$db_record = array(
										 'name' => $name

										,'shares' => $shares
										,'price' =>  $price
										,'total_value' => $shares * $price

										,'id_client' => $id_client
										,'created_at'	=> date('Y-m-d H:i:s')
									);

				$records[] = $db_record;
			}

		}

		return $records;
	}


	function configContact($contact, $id_client, $type, $id_type = 1)
	{
		$db_record = array(
								 'contact' => $contact

								,'contact_type' => $type
								,'id_phone_type' => $id_type

								,'id_client' => $id_client
								,'created_at'	=> date('Y-m-d H:i:s')
							);

		return $db_record;
	}


	function configRecord($client)
	{


		$id_country = getCountryId($client['country']);

		$db_record = array(
								 'name' 		=> $client['name']
								,'address' 		=> $client['address']
								,'notes' 		=> ''
								,'title' 		=> $client['salutation']
								,'id_country'	=> ($id_country) ? $id_country : '404' //404 = unknown country :p

								,'id_status'			=> 0
								,'id_company'			=> 0
								,'id_registry'			=> 0
								,'id_user_agent'		=> 0
								,'id_employee'		=> 0
								,'id_reference'	=> 0
								,'id_user_registry'		=> 0

								,'created_at'	=> date('Y-m-d H:i:s')

							);

		//printr($db_record);

		return $db_record;
	}


	function getClient($name)
	{
		global $db;

		$value = $db->real_escape_string(trim($name));
		$query = "SELECT c.*, s.name as status_name, l.name as list_name
					FROM
						clients c, statuses s, lists l
					WHERE
						c.id_status = s.id AND
						c.id_list = l.id AND
						c.name LIKE \"%$value%\"";

		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			while($r = $db->fetch_object($rs))
			{
				$r->notes = '';
				$dups[] = $r;
			}

			return $dups;
		}
		else
			return false;

	}

	function getPhone($phone)
	{
		global $db;

		$value = $db->real_escape_string(trim($phone));
		$query = "SELECT id_client
					FROM
						client_contacts
					WHERE
						contact LIKE \"%$value%\" LIMIT 1";

		$rs = $db->query($query);
		$r = $db->fetch_object($rs);

		if($db->num_rows($rs))
			return $r->id_client;
		else
			return false;

	}


	function getCallingCode($country)
	{

		global $db;

		$value = $db->real_escape_string(trim($country));

		$clause = (strlen($value)==2) ? "iso3166 LIKE '%$value%'" :  "name LIKE '%$value%'";
		$query = "SELECT calling_code FROM countries WHERE $clause ORDER BY id LIMIT 1";

		//echo $query;

		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			$r = $db->fetch_object($rs);
			return $r->calling_code;
		}
		else
			return false;

	}

	function getCountryId($country)
	{

		global $db;

		$value = $db->real_escape_string(trim($country));
		$clause = (strlen($value)==2) ? "iso3166 LIKE '%$value%'" :  "name LIKE '%$value%'";
		$query = "SELECT id FROM countries WHERE $clause ORDER BY id LIMIT 1";
		//echo $query;

		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			$r = $db->fetch_object($rs);
			return $r->id;
		}
		else
			return false;

	}


	function getId($table, $field, $value, $extra = '')
	{

		global $db;

		$value = $db->real_escape_string(trim($value));
		$query = "SELECT id FROM $table WHERE $field LIKE '%$value%' $extra LIMIT 1";

		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			$r = $db->fetch_object($rs);
			return $r->id;
		}
		else
			return false;

	}
//die();
?>
