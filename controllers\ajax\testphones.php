<?php

	$query = "select id_client, contact
				from client_contacts
				WHERE contact_type = 'phone'
				AND id_client > 16000";


	$rs_contacts = $db->query($query);
	$cont = 0;

	while($c = $db->fetch_object($rs_contacts))
	{
		if(strlen($c->contact) > 4)
		{
			$query = "select id_client from client_contacts
						WHERE contact_type = 'phone'
						AND id_client < 16000
						AND contact LIKE '$c->contact'";

			$rs_exists = $db->query($query);
			if($db->num_rows($rs_exists))
			{
				//echo '<br><br>'.$query.'.......'.$db->num_rows($rs_exists);
				//printr($r);
				//printr($r);
				$dup = $db->fetch_object($rs_exists);

				$db->query("DELETE FROM clients WHERE id = $c->id_client");
				$db->query("DELETE FROM client_audit WHERE id_client = $c->id_client");
				$db->query("DELETE FROM client_contacts WHERE id_client = $c->id_client");
				$db->query("DELETE FROM client_notes  WHERE id_client = $c->id_client");
				$db->query("DELETE FROM client_shares WHERE id_client = $c->id_client");


				$deleted[$c->id_client] = $dup->id_client;

			}

		}


	}
	echo (isset($deleted) && is_array($deleted) ? count($deleted) : 0).'<br>';
	printr($deleted);