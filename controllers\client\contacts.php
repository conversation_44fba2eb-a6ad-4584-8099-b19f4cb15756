<?
	$action = $var1;
	$subaction = $var2;
	$id_contact = $var3;


	if($curr_client->id):


		////////////////////////////////////////////////////////////
		// PHONES

		$sent_phone = $_REQUEST['sent_phone'];
		if($sent_phone)
		{

			$fields_phone = $_REQUEST['fields_phone'];

			if($subaction == 'addPhone')
			{
				$fields_phone['contact_type'] = 'phone';
				$fields_phone['created_at'] = date('Y-m-d H:i:s');
				$fields_phone['id_client'] = $curr_client->id;

				$query = $db->prepare_insert($fields_phone, 'client_contacts');

				if($db->query($query))
					$msg = "Phone successfully registered.";
			}

			if ( $subaction == 'editPhone')
			{
				$fields_phone['updated_at'] = date('Y-m-d H:i:s');

				$query = $db->prepare_update($fields_phone, 'client_contacts', "WHERE id = $id_contact");
				if($db->query($query))
				{
					$msg = "Phone updated successfully.";

				/*
					//Audit
					$fields_audit['event'] = 'update';
					$fields_audit['field'] = 'client phone';
					$fields_audit['new_value'] = $fields_phone['contact'];
					$fields_audit['old_value'] = '--';
					$fields_audit['id_client'] = $curr_client->id;
					$fields_audit['id_user'] = $current_user['userinfo']->id;
					$fields_audit['created_at'] = date('Y-m-d H:i:s');

					$query = $db->prepare_insert($fields_audit, 'client_audit');
					$db->query($query);
				*/
				}
			}
		}
		if ( $subaction == 'deletePhone')
		{

			$query = "DELETE FROM client_contacts WHERE id = $id_contact";
			if($db->query($query))
				$msg = "Phone deleted successfully.";
		}

		$query = "SELECT c.*,t.name as phone_type FROM client_contacts c, phone_types t WHERE c.id_client = $curr_client->id AND c.contact_type = 'phone' AND c.id_phone_type = t.id ORDER BY c.created_at DESC";
		$rs_phones = $db->query($query);

		if($rs_phones && $db->num_rows($rs_phones) > 0)
			while ( $contact = $db->fetch_object($rs_phones))
			{
				$phones[] = $contact;
				if($subaction == 'editPhone' && $id_contact == $contact->id) $phone = $contact;
			}



		////////////////////////////////////////////////////////////
		// E-mails


		$sent_email = $_REQUEST['sent_email'];
		if($sent_email)
		{

			$fields_email = $_REQUEST['fields_email'];

			if($subaction == 'addEmail')
			{
				$fields_email['contact'] = trim($fields_email['contact']);
				$fields_email['contact_type'] = 'email';
				$fields_email['created_at'] = date('Y-m-d H:i:s');
				$fields_email['id_client'] = $curr_client->id;

				$query = $db->prepare_insert($fields_email, 'client_contacts');

				if($db->query($query))
					$msg = "E-mail successfully registered.";
			}

			if ( $subaction == 'editEmail')
			{
				$fields_email['contact'] = trim($fields_email['contact']);
				$fields_email['updated_at'] = date('Y-m-d H:i:s');

				$query = $db->prepare_update($fields_email, 'client_contacts', "WHERE id = $id_contact");
				if($db->query($query))
					$msg = "E-mail updated successfully.";
			}
		}
		if ( $subaction == 'deleteEmail')
		{

			$query = "DELETE FROM client_contacts WHERE id = $id_contact";
			if($db->query($query))
				$msg = "E-mail deleted successfully.";
		}

		$query = "SELECT  * FROM client_contacts WHERE id_client = $curr_client->id AND contact_type = 'email' ORDER BY created_at DESC";
		$rs_emails = $db->query($query);

		if($rs_emails && $db->num_rows($rs_emails) > 0)
			while ( $contact = $db->fetch_object($rs_emails))
			{
				$emails[] = $contact;
				if($subaction == 'editEmail' && $id_contact == $contact->id) $email = $contact;
			}



		// load phone types
		$so['id_phone_type'] = $db->select_values('id', 'name', 'phone_types', $phone->id_phone_type, "WHERE is_active = '1' ORDER BY name");

	endif;

?>
