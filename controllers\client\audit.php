<?
	if($curr_client)
	{

		$subsection = $var1;
		$table = array(
						'title' => 'Client History',
						'fields' => 'id,event,field,old_value,new_value,created_at'
				);

		$table_fields = $table['fields'];
		$table_fields_parts = explode(',',$table['fields']);


		switch($action)
		{
			//list
			default:


				$filterSent = $_REQUEST['filterSent'];
				$q = $_REQUEST['q'];

				if($filterSent)
				{
					if(strlen($q))
						foreach($table_fields_parts as $table_field)
							$clause .= " ({$table_field} LIKE '%{$q}%') OR";

					if(strlen($clause)) $clause = 'AND ('.substr($clause,0,-3).')';
				}

				$query = "SELECT $table_fields FROM client_audit WHERE id_client = $curr_client->id $clause ORDER BY id DESC";
				$rs = $db->query($query);

				while ( $r = $db->fetch_object($rs)) $records[] = $r;
				$total_records = isset($records) && is_array($records) ? count($records) : 0;

			break;
		}


	}
	else $no_client = true;

?>