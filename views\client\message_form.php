<?php if($no_client): ?>

		<div class="status warning">
        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
			<p>
				<img alt="Warning" src="<?=$base?>img/icons/icon_info.png">
				<span>No Client Selected.</span>
				Please select a client at the Client Search section.
			</p>
		</div>

<? else: ?>


<?
//printr($curr_client);
?>

	<div class="contentcontainer left">


		<div class="headings alt">
	        <h2 class="left">Send E-mail to Client - <?=$curr_client->name?></h2>
	    </div>


		<div class="contentbox">
			<form method='post' action="<?=$base?>client/message_form" id="form-email" enctype="multipart/form-data">
				<input type="hidden" name="sent" value="true">
				<ul>
					<li>
						<table>
							<tbody>

								<tr>
									<td><strong>Select Client E-mail:</strong></td>
									<td>
										<? if(isset($emails) && is_array($emails) && count($emails) > 0): foreach($emails as $email): ?>
												<input name="client_emails" type="radio" value="<?=$email->contact?>" />&nbsp;<?=$email->contact?><br>
										<? endforeach; endif;?>
									</td>
								</tr>

								<tr>
									<td><strong>Who is sending?</strong></td>
									<td colspan=3>
										<input type="radio" name="mail[company-type]" value="company" checked>Send as <strong>Company</strong> - <?=$curr_client->company_name?><br>
										<div style="margin-top:4px; padding: 10px; background-color: #EEE">
											<?="$curr_client->company_agent_name ($curr_client->company_agent_email)"?><br>
										</div>
										<br>
										<input type="radio" name="mail[company-type]" value="registry">Send as <strong>Registry</strong> - <?=$curr_client->registry_name?><br>
										<div style="margin-top:4px; padding: 10px; background-color: #EEE">
											<?="$curr_client->registry_name ($curr_client->registry_email)"?>
										</div>
									</td>
								</tr>
								<tr>
									<td><strong>Subject:</strong></td>
									<td colspan=3>
										<input name="mail[subject]" class="inputbox" style="width:552px;" />
									</td>
								</tr>

								<tr>
									<td><strong>Message:</strong></td>
									<td colspan=3>
										<textarea name="mail[message]" id="email-message" class="wysiwyg" style="width:555px; height:200px;"> </textarea>
									</td>
								</tr>

								<tr id="attachment-1">
									<td><strong>Attachment:</strong></td>
									<td colspan=3><input name="mail_attachments[]" type="file" type="text" class="inputbox" /></td>
								</tr>
							</tbody>
						</table>
						<br />
						<br />
					</li>

					<li>
						<input id="attach-more" class="btn" value="+ Attach More Files..." />
						<input type="submit" class="btn" value="Send Message" />
					</li>

				</ul>

			</form>

		</div>


	</div>

	<br />
<? endif; //noclient?>