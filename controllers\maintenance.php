<?
	$table = $subsection = $var1;
	$action = $var2;
	$id = intval($var3);
	//$action = (empty($action))?'manage':$action;

	$sent = $_REQUEST['sent'];

	$tables = array(

		'lists' => array(
							'title' => 'Lists',
							'fields' => 'id,name,origin,comment'
						),

		'refs' => array(
							'title' => 'References',
							'fields' => 'id,name,email,phone,fax,address,id_user,is_active'
						),

		'positions' => array(
							'title' => 'Positions',
							'fields' => 'id,name,company_type'
						),

		'statuses' => array(
							'title' => 'Status ',
							'fields' => 'id,name'
						),

		'phone_types' => array(
							'title' => 'Phone Types',
							'fields' => 'id,name'
						),

		'trunks' => array(
							'title' => 'Trunks',
							'fields' => 'id,username,password,host'
						),

		'clients' => array(
							'title' => 'Leads',
							'fields' => 'id,title,name,address,id_country,id_status,id_list'
						),
	);

	$table_fields = $tables[$table]['fields'];
	$table_fields_parts = explode(',',$tables[$table]['fields']);

	switch($action)
	{
		case 'add':
		case 'edit':

			$view = 'maintenance/form';


			if($sent)
			{
				$fields_record = $_REQUEST['fields_record'];

				if($action == 'add')
				{
					$fields['created_at'] = date('Y-m-d H:i:s');

					$query = $db->prepare_insert($fields_record, $table);
					if($db->query($query))
					{
						if($table=='clients')
						{
							$id_client = $db->insert_id();
							header("location: {$base}client/profile/{$id_client}");
						}
						else
							header("location: {$base}maintenance/{$table}");
					}

				}

				elseif ( $action == 'edit')
				{
					$fields['updated_at'] = date('Y-m-d H:i:s');

					$query = $db->prepare_update($fields_record, $table, "WHERE id = $id");
					if($db->query($query))
						$msg = ucwords(substr($table,0,-1))." updated successfully";

				}

			}

			if($id)
			{
				$query = "SELECT $table_fields FROM $table WHERE id = $id";
				$rs = $db->query($query);
				$r = $db->fetch_object($rs);
			}


		break;

		case 'delete':
		{
			if(  array_key_exists($table, $tables) )
			{
				$query = "DELETE FROM $table WHERE id = $id";
				$db->query($query);

				//delete list's clients data
				if($table == 'lists')
				{
					$query = "SELECT id FROM clients WHERE id_list = $id";
					$rs = $db->query($query);
					while( $r = $db->fetch_object($rs) )
						$deleted_clients[] = $r;

					$query = "DELETE FROM clients WHERE id_list = $id";
					$db->query($query);


					if(isset($deleted_clients) && is_array($deleted_clients) && count($deleted_clients) > 0)
						foreach($deleted_clients as $dc)
						{

							$query = "DELETE FROM client_shares WHERE id_client = $dc->id";
							@$db->query($query);

							$query = "DELETE FROM client_contacts WHERE id_client = $dc->id";
							@$db->query($query);

							$query = "DELETE FROM client_notes WHERE id_client = $dc->id";
							@$db->query($query);
						}

				}


				//update client´ ref to 0
				if($table == 'refs')
				{
					$query = "SELECT id FROM clients WHERE id_reference = $id";
					$rs = $db->query($query);
					while( $r = $db->fetch_object($rs) )
						$clients2update[] = $r;

					if(isset($clients2update) && is_array($clients2update) && count($clients2update) > 0)
						foreach($clients2update as $dc)
						{

							$query = "UPDATE clients SET id_reference = 0  WHERE id_reference = $id";
							@$db->query($query);

						}

				}




				header("location: {$base}maintenance/{$table}");

			}

			/*

			TODO: delete or update related records.
			switch($table)
			{
				case 'list':
				{
					//delete clients?
					break;
				}
			}
			*/
			break;
		}


		//list
		default:

			if($table == 'clients') header("location: {$base}maintenance/clients/add");

			$view = 'maintenance/manage';

			$filterSent = $_REQUEST['filterSent'];
			$q = $_REQUEST['q'];

			if($filterSent)
			{
				if(strlen($q))
					foreach($table_fields_parts as $table_field)
						$clause .= " ({$table_field} LIKE '%{$q}%') OR";

				if(strlen($clause)) $clause = 'WHERE '.substr($clause,0,-3);
			}

			if($table=='users' || $table=='trunks') $orderby = 'username';
			if($table == 'lists') $orderby = 'id DESC';
			if($table == 'statuses') $orderby = 'id';
			if($table == 'positions') $orderby = 'company_type, name';
			if(!strlen($orderby)) $orderby = 'name';

			$query = "SELECT $table_fields FROM $table $clause ORDER BY $orderby";
			$rs = $db->query($query);

			$records = [];
			while ( $r = $db->fetch_object($rs)) $records[] = $r;
			$total_records = isset($records) && is_array($records) ? count($records) : 0;

		break;
	}



?>