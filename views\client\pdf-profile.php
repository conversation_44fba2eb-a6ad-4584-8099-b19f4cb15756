
<? if($no_client): ?>

		<div class="status warning">
        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
			<p>
				<img alt="Warning" src="<?=$base?>img/icons/icon_info.png">
				<span>No Client Selected.</span>
				Please select a client at the Client Search section.
			</p>
		</div>

<? else: ?>


<style>
	body{ background:none; font-size: 11px; padding: 15px; }
	#rightside{ margin:0px;}
	.headings h2 { font-size: 12px;}
	.headings{ height:35px; border-radius: 0; padding: 0 10px;}
	#notes{ font-size:11px; border:1px solid #CCC; padding:15px;}
	.contentbox{ padding:5px; }
	.contentbox p{ padding: 0; }
	select, td{ font-size:11px; }
	th{ font-size:12px; background:none; }

</style>


	<!-- CONTAINER CLIENT INFO -->
		<div class="contentcontainer sml left">

			<div class="headings alt">


				<h2>
                	<?=$client->id?> - <?=$client->title?> <?=$client->name?>
                </h2>
            </div>

			<div class="contentbox">

            	<table>
                    <tbody>
						<tr>
							<td>
								<strong>Company</strong><br>
									<?=$client->company_name?>

							</td>

						</tr>
						<tr>
                        	<td>

								<strong>Registry:</strong><br />
									<?=$client->registry_name?>

							</td>
						</tr>
						<tr>
                        	<td>
								<strong>Company Agent</strong><br />
								<?=$client->company_agent_name?><br />
								<?=$client->company_agent_email?><br />
								(<?=$client->company_user_name?>)
							</td>

						</tr>
						<tr>

							<td>
								<strong>Registry Agent</strong><br />
								<?=$client->registry_agent_name?><br />
								<?=$client->registry_agent_email?><br />
								(<?=$client->registry_user_name?>)
							</td>
						</tr>
						<tr>
							<td>
								<strong>Status</strong><br />
								<select id="client_status">
									<?=$client_statuses?>
								</select>
							</td>


						</tr>

                    </tbody>
                </table>


			</div>

        </div>








		<!-- CONTAINER CONTACT INFO -->
		<div class="contentcontainer med right">

		    <div class="headings alt">
                <h2 class="left">Contact Information</h2>
            </div>

			<div class="contentbox">

            	<table>
                    <tbody>

						<tr>
                        	<td><strong>List</strong></td>
                        	<td><?=$client->list_name?></td>
						</tr>

						<tr class='alt'>
                        	<td><strong>Address</strong></td>
                        	<td><?=$client->address?></td>
						</tr>

						<tr>
                        	<td><strong>E-mail(s)</strong></td>
                        	<td>
								<? if(isset($emails) && is_array($emails) && count($emails) > 0) foreach($emails as $email) echo $email->contact.'<br />'; ?>
							</td>
						</tr>

						<tr  class='alt'>
                        	<td><strong>Contact(s)</strong></td>
                        	<td>

				            	<table>
								<? if(sizeof($phones)) foreach( $phones as $phone ): ?>

									<tr>
										<td>
											<?=$phone->phone_type?>&nbsp;

											<?
												if($phone->contact_type == 'phone')
												{
													$phone->contact = str_replace(' ', '', $phone->contact);
													if(substr($phone->contact, 0, 2) == '00')
														$phone->contact = substr($phone->contact,2);
												}
											?>
											<?=$phone->contact?>&nbsp;
										</td>
										<td>
										</td>
									</tr>
								<? endforeach; ?>
								</table>

							</td>
						</tr>

                    </tbody>
                </table>


			</div>

        </div>




		<!-- CONTAINER CLIENT SHARES -->

		<div class="contentcontainer med right">

		    <div class="headings alt">
                <h2 class="left">Client Shares</h2>

            </div>

			<div class="contentbox">


            	<table>
					<tbody>
						<tr>
							<th style="width:140px;">Name</th>
							<th>Qty</th>
							<th>Each</th>
							<th>Total</th>
						</tr>

						<? if(sizeof($shares)) foreach($shares as $share): ?>

						<tr>

							<? $onchange = "updateClientShare({$share->id});"; ?>

							<td><strong><?=$share->name?></strong></td>

							<td><input value="<?=$share->shares?>" id="share_qty_<?=$share->id?>" onchange="<?=$onchange?>" class="inputbox minibox" /></td>
							<td><input value="<?=$share->price?>" id="share_price_<?=$share->id?>" onchange="<?=$onchange?>" class="inputbox minibox money" /></td>

							<td>
								<strong>
									<span class="money" id="share_total_value_<?=$share->id?>"><?=$share->total_value?></span>
								</strong>
							</td>

						</tr>

						<? endforeach; ?>

                    </tbody>
                </table>
			</div>

        </div>

<!--

		<div class="contentcontainer sml left">

		    <div class="headings alt">
                <h2 class="left">Client History</h2>

            </div>


			<div class="contentbox" id="tab-history">

				<table>
					<tbody>
						<tr>
							<th colspan=2>Status History</th>
						</tr>

						<? if(sizeof($client_history['status'])) foreach($client_history['status'] as $status_change): ?>

						<tr>

							<td><?=$status_change->name?></td>
							<td><?=$status_change->created_at?></td>
						</tr>

						<? endforeach; ?>

						<tr>
							<th colspan=2>Agent History</th>
						</tr>

						<? if(sizeof($client_history['agents'])) foreach($client_history['agents'] as $agent_change): ?>

						<tr>

							<td>

								<?=$agent_change->name?> (<?=$agent_change->username?>)
								<? if($agent_change->field == 'id_employee_registry'): ?><strong style='float:right;'>-- Registry --</strong><? endif?>
							</td>
							<td><?=$agent_change->created_at?></td>
						</tr>

						<? endforeach; ?>


					</tbody>
				</table>

			</div>

    	</div>


-->
		<div style="clear:both;"></div>

		<!-- CONTAINER CLIENT NOTES -->
		<div class="contentcontainer left" id="profile_tabs">

		    <div class="headings alt">

                <h2 class="left">Notes</h2>

            </div>



			<?
			//	$client->notes = trim(strip_tags(str_replace( array('</p>','<br>'),"\r\n", $client->notes )));

				$client->notes = str_replace(array("\r\n","\r","\n"),"",$client->notes);
				/*
				<textarea id="client_notes" class="wysiwyg" style="width:100%; height:600px;"><?=$client->notes?></textarea>
				*/

			?>



			<div class="contentbox" id="notes">

				<br />

				<? echo $client->notes."\r\n"; ?>

				<br />



				<!--
				<h2>New Notes</h2>


				<div id='notes-panel'>loading...</div>

				-->

			</div>


        </div>


		<br />
		<br />


<? endif; //noclient?>