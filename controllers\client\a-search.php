<?
	$t1 = microtime(true);

	//Client Search
	$filterSent = $_REQUEST['filterSent'];
	$filters = $_SESSION['filters'] = ($filterSent) ? $_REQUEST['filters'] : $_SESSION['filters']; //if new filters were sent, get it from request and update session.

	//printr($_SESSION);

	if($var2 == 'clearFilters')
	{
		session_start();
		unset($filters, $_SESSION['filters'],$_SESSION['page']);
		header("location: {$base}client/search/1");
	}


	//id suppress other filters, without losing them
	if(strlen($filters['id'])>0)
	{
		$filter_id = $filters['id']; $temp_filters = $filters; unset($filters); $filters['id'] = $filter_id;
	}

	//manage search filters
	if(isset($filters) && is_array($filters) && count($filters) > 0)
	{
		foreach($filters as $filter_name => $filter):

			if(!empty($filter) || $filter==='0'):

				$filter = addslashes($filter);

				switch($filter_name):

					case 'id':
					case 'id_status':
					case 'id_list':
					{

						$filter_clause .= "AND client.{$filter_name} = $filter ";
						break;
					}

					case 'id_user_agent':
					{
						$condition = ($filter == 0) ? 'AND' : 'OR';
						$filter_clause .= "AND (client.id_user_agent = $filter {$condition} client.id_user_registry = $filter)";
						break;
					}

					case 'client':
					case 'country':
					{
						if($filter_name == 'country' && strlen($filter)==2)
							$filter_clause .= "AND country.iso3166 LIKE '%$filter%' ";
						else
							$filter_clause .= "AND {$filter_name}.name LIKE '%$filter%' ";

						break;
					}


					case 'company':
					{

						$filter_clause .= "AND (company.name LIKE '%$filter%' OR registry.name LIKE '%$filter%')";
						break;
					}

					case 'contact':
					{

						if(strlen($filter) > 2){
							$filter_clause .= "
							AND (
								client.id IN (
									SELECT distinct(id_client) FROM client_contacts WHERE contact LIKE '%{$filter}%'
								)
							)";
						}


						break;
					}

					default: $filter_clause = NULL; break;

				endswitch;

			endif;

		endforeach;

	}

	if(strlen($filters['id'])>0) $filters = $temp_filters;

	$can_see_all_clients = array(1,3);
	if ( !in_array($current_user['userinfo']->id_group, $can_see_all_clients) )
	{

		//Limit results to clients assigned to this user
		$user_id_types = array('agent','registry');
		$user_id = $current_user['userinfo']->id;

		foreach( $user_id_types as $id_type)
			$user_filter .= " client.id_user_{$id_type} = $user_id OR ";

		$user_filter = substr($user_filter,0,-4);
		$filter_clause .= " AND ( $user_filter ) ";


	}

	//Don't show leads where "is_canceled" flag is on, except for groups admin and tech
	$can_see_cancelled = array(1,3);
	if ( ! in_array($current_user['userinfo']->id_group, $can_see_cancelled) ) $filter_clause .= " AND client.is_canceled = 0 ";


	//Don't show heat (status 10) leads except for tech
	$can_see_heat = array(3);
//	if ( ! in_array($current_user['userinfo']->id_group, $can_see_heat) ) $filter_clause .= " AND client.id_status <> 10 ";


	//main_query
	$query = "SELECT
					client.id, client.title, client.name, client.id_status, client.id_list,

					company.name as company_name, company.address as company_address,

					company_agent.name as company_agent_name,
					company_agent.email as company_agent_email,

					registry.name as registry_name, registry.address as registry_address,

					registry_agent.name as registry_agent_name,
					registry_agent.email as registry_agent_email,


					company_user.username as company_user_name,
					registry_user.username as registry_user_name,

					country.iso3166 as country_name,
					status.name as status_name,
					list.name as list_name


				FROM
					clients client,
					companies company,
					companies registry,
					countries country,
					statuses status,
					lists list,
					users company_user,
					users registry_user,
					company_employees company_agent,
					company_employees registry_agent

				WHERE
						client.id_country = country.id
					AND client.id_company = company.id
					AND company.id_registry = registry.id
					AND client.id_user_agent = company_user.id
					AND client.id_user_registry = registry_user.id
					AND client.id_employee = company_agent.id
					AND client.id_employee_registry = registry_agent.id
					AND client.id_status = status.id
					AND client.id_list = list.id

					AND client.is_active = 1

					$filter_clause

					ORDER BY client.id
				";	//client.id_status, client.name


	//manage pagination
	$rows_per_page = 80; //rows per page

//die($page);
	session_start();
	$page = $_SESSION['page'];
	$page = ($page>0)?$page:1;

	if($var2) $page = $_SESSION['page'] = $var2; //get current page from url (url/$interface/$var1/$var2 = url/client/search/page)

	$rs_all_clients = $db->query($query);

	$total_clients = $db->num_rows($rs_all_clients);
	$total_pages = ceil( $total_clients / $rows_per_page);


	//create index for prev and next buttons with all results
	$all_results = [];
	while($client = $db->fetch_object($rs_all_clients)) $all_results[] = $client;
	$all_results_count = isset($all_results) && is_array($all_results) ? count($all_results) : 0;
	for($i=0; $i<$all_results_count; $i++)
	{
		$client_nav[$all_results[$i]->id]->previous_id = $all_results[$i-1]->id;
		$client_nav[$all_results[$i]->id]->next_id = $all_results[$i+1]->id;
	}
	$_SESSION['client_nav'] = $client_nav;


	//create html and clause for pagination
	for( $i=1; $i <= $total_pages; $i++)
	{
		$page_selected = ($i == $page) ? 'class="page"':'';
		$pagination .= "<li $page_selected><a href=\"{$base}client/search/{$i}\">{$i}</a></li>";
	}

	if($page > 1) 			 $pagination  = "<li class=\"text\"><a href=\"{$base}client/search/".($page-1).'">Previous</a></li>'.$pagination;
	if($page < $total_pages) $pagination .= "<li class=\"text\"><a href=\"{$base}client/search/".($page+1).'">Next</a></li>';

	$limit_clause = 'LIMIT '.(($page-1) * $rows_per_page).','.$rows_per_page;
	$query .= $limit_clause;


	//filters and pagination added to query, now execute and add results to array
	$rs_clients = $db->query($query);
	while($client = $db->fetch_object($rs_clients))
		$clients[] = $client;


	$clients_on_page = (isset($clients) && is_array($clients) && count($clients)==$total_clients) ? ' All shown on this page.' : ' Showing '.(isset($clients) && is_array($clients) ? count($clients) : 0).' on this page.';





	//Load values for select fields
	$so['users'] = $db->select_values('id', 'username', 'users', $filters['id_user_agent'], 'WHERE is_active = 1 AND id_group <> 3'); //get active users except tech and admin
	$so['statuses'] = $db->select_values('id', "CONCAT_WS(' - ', id, name)", 'statuses', $filters['id_status'], "WHERE is_active = 1 ORDER BY id"); //get active statuses
	$so['lists'] = $db->select_values('id', "CONCAT_WS(' - ', id, name)", 'lists', $filters['id_list'], 'WHERE is_active = 1 ORDER BY id DESC, name');	//get active loaders

	$t2 = microtime(true);
	$time = $t2-$t1;


?>