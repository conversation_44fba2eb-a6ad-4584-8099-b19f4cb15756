<?
//	$fax_service = 'fax.ararahost.com';
	$fax_service = 'efaxsend.com';
	$fax_from = '<EMAIL>';

	include('controllers/client/profile.php');

	if($_REQUEST['sent'])
	{

		$client_fax = $_REQUEST['client-fax'];
		$mail = $_REQUEST['mail'];

		if(strlen($client_fax))
		{
			require '_lib/mailer/PHPMailerAutoload.php';
			$m = new PHPMailer();

			$m->isSMTP();
			$m->Host = "mail.ararahost.com";
			$m->SMTPDebug = 0; //2
		//	$m->Debugoutput = 'html';

			$m->SMTPAuth = true;
			$m->SMTPSecure = 'tls';
			$m->Port = 587;

			$m->Username = $fax_from;
			$m->Password = "araraclifton";
			$m->setFrom($fax_from, 'Fax Service');


			$m->addAddress($client_fax.'@'.$fax_service);

			$m->Subject = 'Fax';
			$m->Body    = ($mail['company-type']=='company') ? $client->company_name : $client->registry_name;

			$atts = $_FILES['mail_attachments'];
			$total_atts = isset($atts['name']) && is_array($atts['name']) ? count($atts['name']) : 0;

			if($total_atts)
				for($i=0; $i<$total_atts; $i++)
					$m->addAttachment($atts["tmp_name"][$i], $atts["name"][$i]);

			if (!$m->send()) {
			    echo "Mailer Error: " . $m->ErrorInfo.'<br>Please contact tech admin.'; exit();
			}
			else
				$msg = "Fax successfully sent! A confirmation message will be sent to ".$fax_from;
		}
		else
			$msg = 'Please enter a valid fax number for this client.';

	}


	foreach($phones as $phone)
		if($phone->id_phone_type == 5 || $phone->id_phone_type == 2 ){ $client_fax = $phone->contact; break; }
?>