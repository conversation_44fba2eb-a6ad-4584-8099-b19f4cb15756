<?
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

        // Session already started in index.php

        //manage rows per page preference
        if(isset($_REQUEST['rows_per_page']) || isset($_REQUEST['rpp']))
        {
                $rpp = isset($_REQUEST['rows_per_page']) ? intval($_REQUEST['rows_per_page']) : intval($_REQUEST['rpp']);
                if($rpp > 0) {
                    $_SESSION['rows_per_page'] = $rpp;
                    // Reset page to 1 when changing rows per page
                    $_SESSION['page'] = 1;
                }
        }

        $rows_per_page = (isset($_SESSION['rows_per_page'])) ? intval($_SESSION['rows_per_page']) : 25;

        // Debug: uncomment to see what's happening
        // echo "<!-- DEBUG: rows_per_page = $rows_per_page, REQUEST rows_per_page = " . ($_REQUEST['rows_per_page'] ?? 'not set') . ", SESSION rows_per_page = " . ($_SESSION['rows_per_page'] ?? 'not set') . " -->";

        $t1 = microtime(true);

	//Client Search
	$filterSent = $_REQUEST['filterSent'];
	$filters = $_SESSION['filters'] = ($filterSent) ? $_REQUEST['filters'] : $_SESSION['filters']; //if new filters were sent, get it from request and update session.

	// Initialize filters as empty array if null
	if(!is_array($filters)) {
		$filters = array();
	}

//printr($_SESSION);
//die();

	if($var2 == 'clearFilters')
	{
		unset($filters, $_SESSION['filters'],$_SESSION['page']);
		header("location: {$base}client/search/1");
		exit();
	}


	//id suppress other filters, without losing them
	if(isset($filters['id']) && strlen($filters['id'])>0 && is_numeric($filters['id']))
	{
		$filter_id = $filters['id']; $temp_filters = $filters; unset($filters); $filters['id'] = $filter_id;
	}

	//manage search filters
	$filter_clause = null;
	if(isset($filters) && is_array($filters) && count($filters) > 0)
	{
		foreach($filters as $filter_name => $filter):

			if(!empty($filter) || $filter==='0'):

				$filter = addslashes($filter);

				switch($filter_name):

					case 'id':
					case 'id_status':
					case 'id_list':
					{

						$filter_clause .= " AND client.{$filter_name} = $filter ";
						break;
					}

					case 'id_user_agent':
					{
						$condition = ($filter == 0) ? 'AND' : 'OR';
						$filter_clause .= " AND (client.id_user_agent = $filter {$condition} client.id_user_registry = $filter)";
						break;
					}

					case 'client':
					case 'country':
					{
						if($filter_name == 'country' && strlen($filter)==2)
							$filter_clause .= " AND country.iso3166 LIKE '%$filter%' ";
						else
							$filter_clause .= " AND {$filter_name}.name LIKE '%$filter%' ";

						break;
					}


					case 'company':
					{

						$filter_clause .= " AND (company.name LIKE '%$filter%' OR registry.name LIKE '%$filter%')";
						break;
					}

					case 'contact':
					{

						if(strlen($filter) > 2){
							$filter_clause .= "
							AND (
								client.id IN (
									SELECT distinct(id_client) FROM client_contacts WHERE contact LIKE '%{$filter}%'
								)
							)";
						}


						break;
					}

					case 'stock':
					{

						if(strlen($filter) > 4){
							$filter_clause .= "
							AND (
								client.id IN (
									SELECT distinct(id_client) FROM client_shares WHERE name LIKE '%{$filter}%'
								)
							)";
						}


						break;
					}

					default: $filter_clause = NULL; break;

				endswitch;

			endif;

		endforeach;

	}

	if(isset($filters['id']) && strlen($filters['id'])>0) $filters = $temp_filters;

	$can_see_all_clients = array(1,3);
	if ( !in_array($current_user['userinfo']->id_group, $can_see_all_clients) )
	{
///*
		//Limit results to clients assigned to this user
		$user_id_types = array('agent','registry');
		$user_id = $current_user['userinfo']->id;

		foreach( $user_id_types as $id_type)
			$user_filter .= " client.id_user_{$id_type} = $user_id OR ";

		$user_filter = substr($user_filter,0,-4);
		$filter_clause .= " AND ( $user_filter ) ";

//*/
	}

	//Don't show leads where "is_canceled" flag is on, except for groups admin and tech
	$can_see_cancelled = array(1,3);
	if ( ! in_array($current_user['userinfo']->id_group, $can_see_cancelled) ) $filter_clause .= " AND client.is_canceled = 0 ";

	//Don't show heat (status 10) leads except for tech
	$can_see_heat = array(3);
//	if ( ! in_array($current_user['userinfo']->id_group, $can_see_heat) ) $filter_clause .= " AND client.id_status <> 10 ";


	//main_query
	$query = "SELECT
					client.id, client.title, client.name, client.id_status, client.id_list,

					company.name as company_name, company.address as company_address,
					company.city as company_city, company.nationality as company_nationality,
					company.country as company_country,

					company_agent.name as company_agent_name,
					company_agent.email as company_agent_email,

					registry.name as registry_name, registry.address as registry_address,

					registry_agent.name as registry_agent_name,
					registry_agent.email as registry_agent_email,


					company_user.username as company_user_name,
					registry_user.username as registry_user_name,

					country.iso3166 as country_name,
					status.name as status_name,
					list.name as list_name


				FROM
					clients client,
					companies company,
					companies registry,
					countries country,
					statuses status,
					lists list,
					users company_user,
					users registry_user,
					company_employees company_agent,
					company_employees registry_agent

				WHERE
						client.id_country = country.id
					AND client.id_company = company.id
					AND company.id_registry = registry.id
					AND client.id_user_agent = company_user.id
					AND client.id_user_registry = registry_user.id
					AND client.id_employee = company_agent.id
					AND client.id_employee_registry = registry_agent.id
					AND client.id_status = status.id
					AND client.id_list = list.id

					AND client.is_active = 1

					$filter_clause

					ORDER BY client.id
				";	//client.id_status, client.name




        //manage pagination

//die($page);
	$page = (isset($_SESSION['page']) && $_SESSION['page'] > 0) ? $_SESSION['page'] : 1;

	if( isset($var2) && $var2 > 0)
		 $page = $_SESSION['page'] = $var2; //get current page from url (url/$interface/$var1/$var2 = url/client/search/page)

	$rs_all_clients = $db->query($query);


	$total_clients = $db->num_rows($rs_all_clients);

	// Handle "ALL" option - if rows_per_page is very large, show all results on one page
	if($rows_per_page >= 999999) {
	    $rows_per_page = $total_clients > 0 ? $total_clients : 1;
	    $total_pages = 1;
	} else {
	    $total_pages = ceil( $total_clients / $rows_per_page);
	}

	//create index for prev and next buttons with all results
	while($client = $db->fetch_object($rs_all_clients)){
		$all_results[] = $client->id;
		$i++;
		if($i>500) break;
	}
	$_SESSION['client_nav'] = $all_results;

//*/

        // Calculate the current range of results for the text indicator
        if($total_pages == 1 && $rows_per_page >= $total_clients) {
            // Showing all results
            $result_range = "Showing all $total_clients results";
        } else {
            $start_result = (($page - 1) * $rows_per_page) + 1;
            $end_result = min($start_result + $rows_per_page - 1, $total_clients);
            $result_range = "Showing $start_result-$end_result of $total_clients results";
        }

        // Build filter query string for pagination links
        $filter_query_string = '';
        if(isset($filters) && is_array($filters)) {
            $filter_params = array();
            foreach($filters as $filter_name => $filter_value) {
                if(!empty($filter_value) || $filter_value === '0') {
                    $filter_params[] = 'filters[' . urlencode($filter_name) . ']=' . urlencode($filter_value);
                }
            }
            if(!empty($filter_params)) {
                $filter_query_string = '&filterSent=1&' . implode('&', $filter_params);
            }
        }

        // Create html for modern pagination with limited page buttons
        $pagination = '';

        // Only show pagination if there are multiple pages
        if($total_pages > 1) {
            // Add Previous button
            if ($page > 1) {
                $pagination .= '<li class="page-item prev"><a href="' . $base . 'client/search/' . ($page - 1) . '?rows_per_page=' . $rows_per_page . $filter_query_string . '"><span aria-hidden="true">&laquo;</span> Previous</a></li>';
            } else {
                $pagination .= '<li class="page-item prev disabled"><span><span aria-hidden="true">&laquo;</span> Previous</span></li>';
            }

            // Define how many page numbers to show before and after the current page
            $pages_to_show_before_after = 2;

            // Always show first page
            if ($page > 1 + $pages_to_show_before_after) {
                $pagination .= '<li class="page-item"><a href="' . $base . 'client/search/1?rows_per_page=' . $rows_per_page . $filter_query_string . '">1</a></li>';

                // Add ellipsis if there's a gap
                if ($page > 2 + $pages_to_show_before_after) {
                    $pagination .= '<li class="page-item disabled ellipsis"><span>...</span></li>';
                }
            }

            // Show pages around current page
            for ($i = max(1, $page - $pages_to_show_before_after); $i <= min($total_pages, $page + $pages_to_show_before_after); $i++) {
                $page_selected = ($i == $page) ? 'class="page-item active"' : 'class="page-item"';
                $pagination .= '<li ' . $page_selected . '><a href="' . $base . 'client/search/' . $i . '?rows_per_page=' . $rows_per_page . $filter_query_string . '">' . $i . '</a></li>';
            }

            // Always show last page
            if ($page < $total_pages - $pages_to_show_before_after) {
                // Add ellipsis if there's a gap
                if ($page < $total_pages - 1 - $pages_to_show_before_after) {
                    $pagination .= '<li class="page-item disabled ellipsis"><span>...</span></li>';
                }

                $pagination .= '<li class="page-item"><a href="' . $base . 'client/search/' . $total_pages . '?rows_per_page=' . $rows_per_page . $filter_query_string . '">' . $total_pages . '</a></li>';
            }

            // Add Next button
            if ($page < $total_pages) {
                $pagination .= '<li class="page-item next"><a href="' . $base . 'client/search/' . ($page + 1) . '?rows_per_page=' . $rows_per_page . $filter_query_string . '">Next <span aria-hidden="true">&raquo;</span></a></li>';
            } else {
                $pagination .= '<li class="page-item next disabled"><span>Next <span aria-hidden="true">&raquo;</span></span></li>';
            }
        }

	$limit_clause = ' LIMIT '.(($page-1) * $rows_per_page).','.$rows_per_page;
	$query .= $limit_clause;

	// Debug: show the limit clause
	// echo "<!-- DEBUG: LIMIT clause = $limit_clause, page = $page, rows_per_page = $rows_per_page -->";



	//filters and pagination added to query, now execute and add results to array
	$rs_clients = $db->query($query);


	while($client = $db->fetch_object($rs_clients))
		$clients[] = $client;


	$clients_on_page = $result_range;


	//Load values for select fields
	$so['users'] = $db->select_values('id', 'username', 'users', $filters['id_user_agent'] ?? null, 'WHERE is_active = 1 AND id_group <> 3'); //get active users except tech and admin
	$so['statuses'] = $db->select_values('id', "CONCAT_WS(' - ', id, name)", 'statuses', $filters['id_status'] ?? null, "WHERE is_active = 1 ORDER BY id"); //get active statuses
	$so['lists'] = $db->select_values('id', "CONCAT_WS(' - ', id, name)", 'lists', $filters['id_list'] ?? null, 'WHERE is_active = 1 ORDER BY id DESC, name');	//get active loaders

	$t2 = microtime(true);
	$time = $t2-$t1;


?>
