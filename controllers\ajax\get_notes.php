<?
	$id = $var2;

	//get notes
	$query = "SELECT cn.*, u.username as user FROM client_notes cn, users u WHERE cn.id_client = $id AND cn.id_user = u.id ORDER BY cn.created_at DESC";
	$notes = $db->getQueryList($query);

//printr($current_user);
?>
				<table class="notes-table">
				<?

					if(isset($notes) && is_array($notes) && count($notes) > 0):
						foreach($notes as $note):

							$str_sep = str_repeat("-", strlen($note->user));

							if(isset($last_user) && $note->user <> $last_user):
							?>
								<tr class="note-user">
									<td colspan=3><?=$note->user?></td>
								</tr>

							<?
							endif;

							$last_user = $note->user;

							?>
								<tr class="note-each">
									<td class="note-timestamp" style="width:95px">
										<small><strong><?=date('jS M  Y h:ia', strtotime($note->created_at));?></strong></small>
									</td>
									<td class="note-text"><?=stripslashes($note->note)?></td>

									<!-- <td class="note-timestamp"><?=date('d/m/y H:ia', strtotime($note->created_at));?></td> -->


									<td class="note-delete"><?

										if($note->id_user == $current_user['userinfo']->id): ?><button class="btn" onclick="deleteNote('<?=$note->id?>');">x</button>
										<? else: ?>--<? endif; ?>
									</td>
								</tr>
							<?

						endforeach;
					else: echo("<tr><td>You haven't added notes for this client yet.</td></tr>");
					endif;

				?>
				</table>
