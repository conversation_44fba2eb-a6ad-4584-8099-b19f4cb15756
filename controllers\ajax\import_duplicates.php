<?

	if($save)
	{

		$client = configRecord($info);
		$client['id_list'] = $id_list;

		//printr($client);

		$query = $db->prepare_insert($client, 'clients');
		$db->query($query);

		$id_client = $db->insert_id();

		foreach($phones as $phone)
		{
			if(strlen($phone))
			{
				$db_contact = configContact($phone, $id_client, 'phone');
				$query = $db->prepare_insert($db_contact, 'client_contacts');
				$db->query($query);
			}
		}

		if(strlen($info['Email 1']))
		{
			$db_contact = configContact($info['Email 1'], $id_client, 'email');
			$query = $db->prepare_insert($db_contact, 'client_contacts');
			$db->query($query);
		}


		if(strlen($info['Fax']))
		{
			$db_contact = configContact($info['Fax'], $id_client, 'phone', 2);
			$query = $db->prepare_insert($db_contact, 'client_contacts');
			$db->query($query);
		}


		unset($shares);
		$shares = configShares($info, $id_client, 8);
		if(isset($shares) && is_array($shares) && count($shares) > 0)
		{
			foreach($shares as $share)
			{
				$query = $db->prepare_insert($share, 'client_shares');
				$db->query($query);
			}

		}

	}
?>